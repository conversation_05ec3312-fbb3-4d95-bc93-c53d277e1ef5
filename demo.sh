#!/bin/bash

# Demo script for ICI Network Diagnostics TUI

echo "🔗 ICI Network Diagnostics TUI Demo"
echo "===================================="
echo ""
echo "This TUI application provides:"
echo "• Full-screen terminal interface"
echo "• Horizontal layout of 16 QSFP ports"
echo "• Real-time network diagnostics"
echo "• Mouse and keyboard navigation"
echo "• Live status updates"
echo ""
echo "Controls:"
echo "• ←/→ Arrow Keys: Navigate between ports"
echo "• Home/End: Jump to first/last port"
echo "• Mouse: Click on ports to select"
echo "• Q or Ctrl+C: Quit"
echo ""
echo "Starting the TUI in 3 seconds..."
echo "Press Ctrl+C to cancel"

sleep 1
echo "3..."
sleep 1
echo "2..."
sleep 1
echo "1..."
sleep 1

echo ""
echo "🚀 Launching ICI Network Diagnostics TUI..."
echo ""

# Run the TUI application
go run main.go
