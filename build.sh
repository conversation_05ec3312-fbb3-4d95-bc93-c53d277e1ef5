#!/bin/bash

# Build script for ICI Network Diagnostics TUI

echo "Building ICI Network Diagnostics TUI..."

# Clean any previous builds
rm -f ici-diagnostics

# Build for current platform
go build -o ici-diagnostics main.go

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "Run with: ./ici-diagnostics"
else
    echo "❌ Build failed!"
    exit 1
fi

# Optional: Build for multiple platforms
echo ""
echo "Building for multiple platforms..."

# Linux
GOOS=linux GOARCH=amd64 go build -o ici-diagnostics-linux-amd64 main.go
echo "✅ Linux build complete"

# Windows
GOOS=windows GOARCH=amd64 go build -o ici-diagnostics-windows-amd64.exe main.go
echo "✅ Windows build complete"

# macOS
GOOS=darwin GOARCH=amd64 go build -o ici-diagnostics-macos-amd64 main.go
echo "✅ macOS build complete"

echo ""
echo "All builds completed successfully!"
echo "Files created:"
echo "  - ici-diagnostics (current platform)"
echo "  - ici-diagnostics-linux-amd64"
echo "  - ici-diagnostics-windows-amd64.exe"
echo "  - ici-diagnostics-macos-amd64"
