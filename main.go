package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Port status constants
const (
	PortStatusUnknown = iota
	PortStatusUp
	PortStatusDown
	PortStatusError
)

// Tab constants
const (
	TabICI = iota
	TabDiag
)

// Layout constants for ICI tab
const (
	QSFP_PORT_WIDTH  = 4
	QSFP_PORT_HEIGHT = 1
	PORT_GROUP_GAP   = 2
	PORTS_PER_GROUP  = 4
	TOTAL_PORTS      = 16

	// Minimum terminal dimensions
	MIN_TERMINAL_WIDTH  = 80
	MIN_TERMINAL_HEIGHT = 27 // Increased for 2-row port layout

	// Layout spacing constants
	HEADER_HEIGHT    = 3 // Title + tabs + spacing
	FOOTER_HEIGHT    = 2 // Controls footer + spacing
	PORT_INFO_HEIGHT = 3 // Selected port info height
)

// Tab styles
var (
	activeTabBorder = lipgloss.Border{
		Top:         "─",
		Bottom:      " ",
		Left:        "│",
		Right:       "│",
		TopLeft:     "╭",
		TopRight:    "╮",
		BottomLeft:  "┘",
		BottomRight: "└",
	}

	tabBorder = lipgloss.Border{
		Top:         "─",
		Bottom:      "─",
		Left:        "│",
		Right:       "│",
		TopLeft:     "╭",
		TopRight:    "╮",
		BottomLeft:  "┴",
		BottomRight: "┴",
	}

	tab = lipgloss.NewStyle().
		Border(tabBorder, true).
		BorderForeground(lipgloss.Color("39")).
		Padding(0, 1)

	activeTab = lipgloss.NewStyle().
			Border(activeTabBorder, true).
			BorderForeground(lipgloss.Color("226")).
			Padding(0, 1)

	tabGap = lipgloss.NewStyle().
		BorderTop(true).
		BorderTopForeground(lipgloss.Color("39")).
		BorderBottom(false).
		BorderLeft(false).
		BorderRight(false)

	// ICI tab style constants
	portColorUp       = lipgloss.Color("46")  // Green
	portColorDown     = lipgloss.Color("196") // Red
	portColorError    = lipgloss.Color("208") // Orange
	portColorUnknown  = lipgloss.Color("240") // Gray
	portColorCursor   = lipgloss.Color("226") // Yellow
	portColorSelected = lipgloss.Color("22")  // Dark green

	switchBorderColor  = lipgloss.Color("62") // Purple
	panelBorderColor   = lipgloss.Color("39") // Blue
	controlBorderColor = lipgloss.Color("46") // Green
)

// QSFPPort represents a single QSFP port
type QSFPPort struct {
	ID     int
	Status int
	Info   string
}

// Model represents the main application state
type Model struct {
	ports          [16]QSFPPort
	selectedPort   int
	selectedPorts  map[int]bool // Track multiple selected ports
	diagnosticData string
	lastUpdate     time.Time
	width          int
	height         int
	mouseEnabled   bool
	isUpdating     bool
	updateCount    int
	machineInput   string
	inputFocused   bool
	selectedICIs   []string // Multiple ICI ports
	activeTab      int      // Current active tab (TabICI or TabDiag)
}

// Messages for the tea program
type tickMsg time.Time
type diagnosticMsg string

// Initialize the model
func initialModel() Model {
	ports := [16]QSFPPort{}
	for i := range 16 {
		ports[i] = QSFPPort{
			ID:     i + 1,
			Status: PortStatusUnknown,
			Info:   fmt.Sprintf("Port %d", i+1),
		}
	}

	model := Model{
		ports:         ports,
		selectedPort:  0,
		selectedPorts: make(map[int]bool),
		mouseEnabled:  true,
		machineInput:  "",
		inputFocused:  false,
		selectedICIs:  []string{}, // Multiple ICI ports
		activeTab:     TabICI,     // Start with ICI tab active
	}
	// Select first port by default
	model.selectedPorts[0] = true
	model.updateSelectedICIs()
	return model
}

// Init initializes the program
func (m Model) Init() tea.Cmd {
	return tea.Batch(
		tea.EnableMouseCellMotion,
		tea.WindowSize(), // Get initial window size
		tickCmd(),
		runDiagnosticCmd(),
	)
}

// Update handles messages
func (m Model) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "i":
			// Switch to ICI tab
			m.activeTab = TabICI
			m.inputFocused = false // Reset input focus when switching tabs
		case "d":
			// Switch to Diag tab
			m.activeTab = TabDiag
			m.inputFocused = false // Reset input focus when switching tabs
		case "tab":
			// Toggle between port selection and machine input (only in ICI tab)
			if m.activeTab == TabICI {
				m.inputFocused = !m.inputFocused
			}
		case "enter":
			if m.inputFocused {
				// Run test with current machine and selected ICI ports
				return m, runTestCmd(m.machineInput, strings.Join(m.selectedICIs, ","))
			}
		case "backspace":
			if m.inputFocused && len(m.machineInput) > 0 {
				m.machineInput = m.machineInput[:len(m.machineInput)-1]
			}
		case " ", "space":
			if !m.inputFocused {
				// Toggle selection of current port
				if m.selectedPorts[m.selectedPort] {
					delete(m.selectedPorts, m.selectedPort)
				} else {
					m.selectedPorts[m.selectedPort] = true
				}
				m.updateSelectedICIs()
			}
		case "left":
			if !m.inputFocused && m.selectedPort > 0 {
				m.selectedPort--
			}
		case "right":
			if !m.inputFocused && m.selectedPort < 15 {
				m.selectedPort++
			}
		case "home":
			if !m.inputFocused {
				m.selectedPort = 0
			}
		case "end":
			if !m.inputFocused {
				m.selectedPort = 15
			}
		default:
			// Handle text input for machine name
			if m.inputFocused && len(msg.String()) == 1 {
				char := msg.String()
				// Allow alphanumeric characters for machine names
				if (char >= "a" && char <= "z") || (char >= "A" && char <= "Z") || (char >= "0" && char <= "9") {
					m.machineInput += char
				}
			}
		}

	case tea.MouseMsg:
		// Handle multiple mouse event types for better compatibility
		isLeftClick := false

		// Check for left click events (using modern API)
		if (msg.Action == tea.MouseActionPress || msg.Action == tea.MouseActionRelease) &&
			msg.Button == tea.MouseButtonLeft {
			isLeftClick = true
		}

		if isLeftClick {
			// Handle mouse clicks on ports
			portIndex := m.getPortFromMouse(msg.X, msg.Y)

			if portIndex >= 0 && portIndex < 16 {
				m.selectedPort = portIndex
				// Toggle selection on click
				if m.selectedPorts[portIndex] {
					delete(m.selectedPorts, portIndex)
				} else {
					m.selectedPorts[portIndex] = true
				}
				m.updateSelectedICIs()
			}
		}

	case tickMsg:
		m.lastUpdate = time.Time(msg)
		m.isUpdating = true
		m.updateCount++
		return m, tea.Batch(tickCmd(), runDiagnosticCmd())

	case diagnosticMsg:
		m.diagnosticData = string(msg)
		m.processDiagnosticData()
		m.isUpdating = false
	}

	return m, nil
}

// isTerminalTooSmall checks if terminal is below minimum size
func (m Model) isTerminalTooSmall() bool {
	return m.width < MIN_TERMINAL_WIDTH || m.height < MIN_TERMINAL_HEIGHT
}

// calculateSwitchHeight calculates the actual height needed for the switch
func (m Model) calculateSwitchHeight() int {
	// Switch components:
	// - Header: 1 line
	// - Empty line: 1 line
	// - "QSFP Ports:" label: 1 line
	// - Port container: depends on layout
	// - Empty line: 1 line
	// - Legend: 1 line
	// - Switch border + padding: 4 lines

	baseHeight := 8 // Header + labels + legend + borders

	if m.shouldUseVerticalLayout() {
		// 2 rows of ports + spacing + container borders
		baseHeight += 6 // 2 port rows + spacing + container padding
	} else {
		// 1 row of ports + container borders
		baseHeight += 4 // 1 port row + container padding
	}

	return baseHeight
}

// calculateAvailableHeight calculates remaining height for content
func (m Model) calculateAvailableHeight() int {
	usedHeight := HEADER_HEIGHT + FOOTER_HEIGHT
	if m.activeTab == TabICI {
		switchHeight := m.calculateSwitchHeight()
		usedHeight += switchHeight + PORT_INFO_HEIGHT
	}
	availableHeight := m.height - usedHeight
	if availableHeight < 5 {
		availableHeight = 5 // Minimum content height
	}
	return availableHeight
}

// renderTooSmallMessage shows a message when terminal is too small
func (m Model) renderTooSmallMessage() string {
	message := fmt.Sprintf(
		"Terminal too small!\n\nCurrent: %dx%d\nMinimum: %dx%d\n\nPlease resize your terminal window.",
		m.width, m.height, MIN_TERMINAL_WIDTH, MIN_TERMINAL_HEIGHT,
	)

	style := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("196")). // Red border
		Padding(1).
		Align(lipgloss.Center).
		Width(min(m.width, 50)).
		Height(min(m.height, 10))

	return lipgloss.Place(m.width, m.height, lipgloss.Center, lipgloss.Center, style.Render(message))
}

// getPortGroupSpacing returns appropriate spacing based on terminal size
func (m Model) getPortGroupSpacing() int {
	if m.width < 90 {
		return 1 // Tighter spacing for smaller terminals
	}
	return PORT_GROUP_GAP
}

// shouldUseVerticalLayout determines if we should stack ports in 2 rows instead of 1
func (m Model) shouldUseVerticalLayout() bool {
	// Calculate horizontal layout width: 16 ports * 6 chars + 3 gaps * spacing + container padding
	spacing := m.getPortGroupSpacing()
	horizontalWidth := (16 * 6) + (3 * spacing) + 20 // 20 for container padding/borders
	return horizontalWidth > m.width || m.width < 100
}

// View renders the UI
func (m Model) View() string {
	if m.width == 0 || m.height == 0 {
		return "Loading..."
	}

	// Handle terminal too small
	if m.isTerminalTooSmall() {
		return m.renderTooSmallMessage()
	}

	var b strings.Builder

	// Title
	title := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("86")).
		Width(m.width).
		Align(lipgloss.Center).
		Render("ICI Network Diagnostics")
	b.WriteString(title + "\n")

	// Render tabs with proper Lipgloss styling
	tabsContent := m.renderTabs()
	b.WriteString(tabsContent + "\n")

	// Content based on active tab
	availableHeight := m.calculateAvailableHeight()
	if m.activeTab == TabICI {
		// ICI tab: Original view with switch visualization and control panel
		b.WriteString(m.renderICIView(availableHeight))
	} else {
		// Diag tab: Full-width diagnostic view
		b.WriteString(m.renderDiagView(availableHeight))
	}

	// Footer with proper styling
	footer := lipgloss.NewStyle().
		Width(m.width).
		Background(lipgloss.Color("235")).
		Foreground(lipgloss.Color("241")).
		Padding(0, 1).
		Render("Controls: i (ICI tab) | d (Diag tab) | q (quit)")

	b.WriteString("\n" + footer)

	return b.String()
}

// renderTabs creates the tab bar
func (m Model) renderTabs() string {
	var iciTabStyle, diagTabStyle lipgloss.Style

	if m.activeTab == TabICI {
		iciTabStyle = activeTab
		diagTabStyle = tab
	} else {
		iciTabStyle = tab
		diagTabStyle = activeTab
	}

	row := lipgloss.JoinHorizontal(
		lipgloss.Top,
		iciTabStyle.Render("ICI"),
		diagTabStyle.Render("Diag"),
	)

	// Fill remaining space with gap - handle zero width gracefully
	if m.width > 0 {
		gapWidth := max(0, m.width-lipgloss.Width(row)-2)
		gap := tabGap.Render(strings.Repeat("─", gapWidth))
		row = lipgloss.JoinHorizontal(lipgloss.Bottom, row, gap)
	}

	return row
}

// renderICIView renders the original ICI view with switch and control panel
func (m Model) renderICIView(diagnosticHeight int) string {
	var b strings.Builder

	// Switch visualization - full width
	b.WriteString(m.renderSwitch())
	b.WriteString("\n")

	// Selected port info - full width
	selectedPortInfo := m.renderSelectedPortInfo()
	b.WriteString(selectedPortInfo)
	b.WriteString("\n")

	// Create unified diagnostic and control panel
	unifiedPanel := m.renderUnifiedPanel(diagnosticHeight)
	b.WriteString(unifiedPanel)

	return b.String()
}

// renderDiagView renders the diagnostic-focused view
func (m Model) renderDiagView(diagnosticHeight int) string {
	var b strings.Builder

	// Diagnostic header
	header := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		Width(m.width).
		Align(lipgloss.Center).
		Render("🔍 Advanced Network Diagnostics")
	b.WriteString(header + "\n\n")

	// Create diagnostic panels
	leftPanel := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("39")).
		Width((m.width / 2) - 2).
		Height(diagnosticHeight - 5).
		Padding(1).
		Render("📊 Performance Metrics\n\n• Throughput Analysis\n• Latency Monitoring\n• Error Rate Tracking\n• Bandwidth Utilization\n\n📈 Real-time Stats\n• Active Connections: 42\n• Data Rate: 1.2 Gbps\n• Packet Loss: 0.01%")

	rightPanel := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("39")).
		Width((m.width / 2) - 2).
		Height(diagnosticHeight - 5).
		Padding(1).
		Render("🔧 Diagnostic Tools\n\n• Port Health Check\n• Signal Quality Test\n• Connectivity Validation\n• Performance Benchmarks\n\n📋 Recent Issues\n• Port 8: Signal degradation\n• Port 12: High error rate\n• Port 3: Intermittent drops")

	// Join panels horizontally
	panels := lipgloss.JoinHorizontal(lipgloss.Top, leftPanel, rightPanel)
	b.WriteString(panels)

	return b.String()
}

// renderSwitch creates the visual representation of the switch
func (m Model) renderSwitch() string {
	var switchParts []string

	// Switch header
	switchHeader := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("39")).
		Width(m.width - 4). // Account for border
		Align(lipgloss.Center).
		Render("ICI Network Switch - 16x QSFP Ports")

	switchParts = append(switchParts, switchHeader)
	switchParts = append(switchParts, "")

	// Choose layout based on terminal width
	var portsDisplay string
	if m.shouldUseVerticalLayout() {
		portsDisplay = m.renderPortsVertical()
	} else {
		portsDisplay = m.renderPortsHorizontal()
	}

	// Create a bordered container for all ports with padding
	portsContainer := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(panelBorderColor).
		Padding(1, 2).
		Align(lipgloss.Center).
		Render(portsDisplay)

	// Center the entire ports container in the switch area
	// Adjust width based on terminal size
	containerWidth := m.width - 6 // Account for switch border
	if containerWidth < 50 {
		containerWidth = max(30, m.width-4) // Minimum usable width
	}

	centeredContainer := lipgloss.NewStyle().
		Width(containerWidth).
		Align(lipgloss.Center).
		Render(portsContainer)

	// Add the centered ports container
	switchParts = append(switchParts, "QSFP Ports:")
	switchParts = append(switchParts, centeredContainer)

	// Port legend
	legend := m.renderPortLegend()
	switchParts = append(switchParts, "")
	switchParts = append(switchParts, legend)

	switchStyle := lipgloss.NewStyle().
		Border(lipgloss.DoubleBorder()).
		BorderForeground(switchBorderColor).
		Width(m.width-2). // Account for border width
		Padding(1, 2)

	return switchStyle.Render(strings.Join(switchParts, "\n"))
}

// renderQSFPPort renders a single QSFP port (1 high x 4 wide) as border only
func (m Model) renderQSFPPort(portIndex int) string {
	port := m.ports[portIndex]

	// Get border color based on status
	var borderColor lipgloss.Color
	switch port.Status {
	case PortStatusUp:
		borderColor = portColorUp
	case PortStatusDown:
		borderColor = portColorDown
	case PortStatusError:
		borderColor = portColorError
	default:
		borderColor = portColorUnknown
	}

	// Create port content (port number)
	portNum := fmt.Sprintf("%2d", port.ID)

	// Create the port with Lipgloss border
	portStyle := lipgloss.NewStyle().
		Width(QSFP_PORT_WIDTH).
		Height(QSFP_PORT_HEIGHT).
		Align(lipgloss.Center).
		Border(lipgloss.NormalBorder()).
		BorderForeground(borderColor)

	// Add highlighting based on selection state
	if portIndex == m.selectedPort {
		portStyle = portStyle.BorderForeground(portColorCursor)
	}

	// Add selection highlighting - background for selected ports
	if m.selectedPorts[portIndex] {
		portStyle = portStyle.Background(portColorSelected)
	}

	return portStyle.Render(portNum)
}

// renderPortsHorizontal renders all ports in a single horizontal row (original layout)
func (m Model) renderPortsHorizontal() string {
	var portBlocks []string

	for i := range TOTAL_PORTS {
		portBlock := m.renderQSFPPort(i)
		portBlocks = append(portBlocks, portBlock)

		// Add spacing after each group of 4 ports (except the last group)
		if (i+1)%PORTS_PER_GROUP == 0 && i < TOTAL_PORTS-1 {
			spacing := m.getPortGroupSpacing()
			spacer := lipgloss.NewStyle().
				Width(spacing).
				Height(QSFP_PORT_HEIGHT + 2). // Account for border height
				Render(strings.Repeat(" ", spacing))
			portBlocks = append(portBlocks, spacer)
		}
	}

	// Use Lipgloss to join ports horizontally
	return lipgloss.JoinHorizontal(lipgloss.Top, portBlocks...)
}

// renderPortsVertical renders ports in 2 rows of 8 ports each for better screen fit
func (m Model) renderPortsVertical() string {
	// Create two rows: ports 1-8 and ports 9-16
	var topRowBlocks []string
	var bottomRowBlocks []string

	// Top row: ports 0-7 (displayed as 1-8)
	for i := range 8 {
		portBlock := m.renderQSFPPort(i)
		topRowBlocks = append(topRowBlocks, portBlock)

		// Add spacing after every 4 ports
		if (i+1)%4 == 0 && i < 7 {
			spacing := m.getPortGroupSpacing()
			spacer := lipgloss.NewStyle().
				Width(spacing).
				Height(QSFP_PORT_HEIGHT + 2).
				Render(strings.Repeat(" ", spacing))
			topRowBlocks = append(topRowBlocks, spacer)
		}
	}

	// Bottom row: ports 8-15 (displayed as 9-16)
	for i := 8; i < 16; i++ {
		portBlock := m.renderQSFPPort(i)
		bottomRowBlocks = append(bottomRowBlocks, portBlock)

		// Add spacing after every 4 ports
		if (i-8+1)%4 == 0 && i < 15 {
			spacing := m.getPortGroupSpacing()
			spacer := lipgloss.NewStyle().
				Width(spacing).
				Height(QSFP_PORT_HEIGHT + 2).
				Render(strings.Repeat(" ", spacing))
			bottomRowBlocks = append(bottomRowBlocks, spacer)
		}
	}

	// Join each row horizontally
	topRow := lipgloss.JoinHorizontal(lipgloss.Top, topRowBlocks...)
	bottomRow := lipgloss.JoinHorizontal(lipgloss.Top, bottomRowBlocks...)

	// Join the two rows vertically with some spacing
	return lipgloss.JoinVertical(lipgloss.Center, topRow, "", bottomRow)
}

// renderPortLegend shows the status legend
func (m Model) renderPortLegend() string {
	legendItems := []string{
		lipgloss.NewStyle().Foreground(portColorUp).Render("●●●● UP"),
		lipgloss.NewStyle().Foreground(portColorDown).Render("○○○○ DOWN"),
		lipgloss.NewStyle().Foreground(portColorError).Render("▲▲▲▲ ERROR"),
		lipgloss.NewStyle().Foreground(portColorUnknown).Render("---- UNKNOWN"),
	}

	legendText := "Legend: " + strings.Join(legendItems, " | ")

	return lipgloss.NewStyle().
		Width(m.width - 6). // Account for switch border and padding
		Align(lipgloss.Center).
		Render(legendText)
}

// renderSelectedPortInfo shows details about the selected port
func (m Model) renderSelectedPortInfo() string {
	port := m.ports[m.selectedPort]

	statusText := "Unknown"
	statusColor := "240"

	switch port.Status {
	case PortStatusUp:
		statusText = "UP"
		statusColor = "46"
	case PortStatusDown:
		statusText = "DOWN"
		statusColor = "196"
	case PortStatusError:
		statusText = "ERROR"
		statusColor = "208"
	}

	// Show current cursor port and all selected ports
	selectedPortsList := make([]string, 0)
	for portIdx := range m.selectedPorts {
		selectedPortsList = append(selectedPortsList, fmt.Sprintf("%d", portIdx+1))
	}

	var selectedInfo string
	if len(selectedPortsList) > 0 {
		selectedInfo = fmt.Sprintf("Selected Ports: [%s]", strings.Join(selectedPortsList, ","))
	} else {
		selectedInfo = "No ports selected"
	}

	info := fmt.Sprintf("Cursor: Port %d | %s | Status: %s | %s",
		port.ID, selectedInfo, statusText, port.Info)

	style := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color(statusColor)).
		Width(m.width-2). // Account for border width
		Padding(0, 1).
		Align(lipgloss.Center)

	return style.Render(info)
}

// renderUnifiedPanel shows both diagnostic output and control interface in one panel
func (m Model) renderUnifiedPanel(height int) string {
	// Ensure minimum height and adjust for small terminals
	minHeight := 10
	if m.height < MIN_TERMINAL_HEIGHT {
		minHeight = max(5, height)
	}
	if height < minHeight {
		height = minHeight
	}

	var panelParts []string

	// Machine Configuration Section
	panelParts = append(panelParts, "🔧 Machine Configuration")
	panelParts = append(panelParts, "")

	// Machine input field
	var machineInputContent string
	if m.inputFocused {
		machineInputContent = fmt.Sprintf("Machine: %s█", m.machineInput) // Show cursor
	} else {
		if m.machineInput == "" {
			machineInputContent = "Machine: (Tab to focus)"
		} else {
			machineInputContent = fmt.Sprintf("Machine: %s", m.machineInput)
		}
	}

	machineStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(panelBorderColor).
		Padding(0, 1)

	if m.inputFocused {
		machineStyle = machineStyle.BorderForeground(portColorCursor) // Yellow when focused
	}

	panelParts = append(panelParts, machineStyle.Render(machineInputContent))
	panelParts = append(panelParts, "")

	// Selected ICI ports section
	panelParts = append(panelParts, "📡 Selected ICI Ports")
	panelParts = append(panelParts, "")

	if len(m.selectedICIs) == 0 {
		panelParts = append(panelParts, "No ports selected")
		panelParts = append(panelParts, "Use Space or click to select QSFP ports")
	} else {
		for i, ici := range m.selectedICIs {
			portNum := i + 1
			for portIdx := range m.selectedPorts {
				if fmt.Sprintf("ici%d", portIdx+1) == ici {
					portNum = portIdx + 1
					break
				}
			}
			iciLine := fmt.Sprintf("• QSFP Port %d → %s", portNum, ici)
			panelParts = append(panelParts, iciLine)
		}
	}

	panelParts = append(panelParts, "")

	// Test execution status
	if m.machineInput != "" && len(m.selectedICIs) > 0 {
		panelParts = append(panelParts, "✅ Ready to test!")
		panelParts = append(panelParts, "Press Enter to run test")
	} else {
		panelParts = append(panelParts, "Need machine name and ports")
	}

	panelParts = append(panelParts, "")
	panelParts = append(panelParts, strings.Repeat("─", 50))
	panelParts = append(panelParts, "")

	// Diagnostic Output Section
	panelParts = append(panelParts, "📊 Diagnostic Output")
	panelParts = append(panelParts, "")

	diagnosticContent := m.diagnosticData
	if diagnosticContent == "" {
		diagnosticContent = "Running diagnostics..."
	}

	// Add diagnostic content with proper line management
	diagnosticLines := strings.Split(diagnosticContent, "\n")

	// Calculate how many lines we can show for diagnostics
	usedLines := len(panelParts) + 2 // +2 for padding
	availableLines := height - usedLines

	if availableLines > 0 && len(diagnosticLines) > availableLines {
		diagnosticLines = diagnosticLines[len(diagnosticLines)-availableLines:] // Show most recent lines
	}

	panelParts = append(panelParts, diagnosticLines...)

	// Create the unified panel
	content := strings.Join(panelParts, "\n")

	style := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(controlBorderColor). // Use green border for the unified panel
		Padding(1).
		Height(height).
		Width(m.width - 2) // Full width minus border

	return style.Render(content)
}

// getPortFromMouse calculates which port was clicked based on mouse coordinates
func (m Model) getPortFromMouse(x, y int) int {
	if m.shouldUseVerticalLayout() {
		return m.getPortFromMouseVertical(x, y)
	} else {
		return m.getPortFromMouseHorizontal(x, y)
	}
}

// getPortFromMouseHorizontal handles mouse clicks for horizontal layout
func (m Model) getPortFromMouseHorizontal(x, y int) int {
	// Check if click is in the port area (approximate vertical bounds)
	if y < 7 || y > 15 {
		return -1
	}

	// Calculate port layout dimensions using dynamic spacing
	portWidth := QSFP_PORT_WIDTH + 2 // Account for border
	groupWidth := PORTS_PER_GROUP * portWidth
	totalGroups := TOTAL_PORTS / PORTS_PER_GROUP
	spacing := m.getPortGroupSpacing()
	totalGaps := (totalGroups - 1) * spacing
	totalPortsWidth := (totalGroups * groupWidth) + totalGaps

	// Find starting position (centered)
	startX := (m.width - totalPortsWidth) / 2

	if x < startX || x > startX+totalPortsWidth {
		return -1
	}

	relativeX := x - startX

	// Determine which group and port within group
	for group := range totalGroups {
		groupStart := group * (groupWidth + spacing)
		groupEnd := groupStart + groupWidth

		if relativeX >= groupStart && relativeX < groupEnd {
			portInGroup := (relativeX - groupStart) / portWidth
			if portInGroup < PORTS_PER_GROUP {
				portIndex := group*PORTS_PER_GROUP + portInGroup
				if portIndex < TOTAL_PORTS {
					return portIndex
				}
			}
		}
	}

	return -1
}

// getPortFromMouseVertical handles mouse clicks for vertical (2-row) layout
func (m Model) getPortFromMouseVertical(x, y int) int {
	// Check if click is in the port area (approximate vertical bounds for 2-row layout)
	if y < 7 || y > 18 {
		return -1
	}

	// Calculate dimensions for 2-row layout
	portWidth := QSFP_PORT_WIDTH + 2 // Account for border
	spacing := m.getPortGroupSpacing()

	// Each row has 8 ports in 2 groups of 4
	rowWidth := (8 * portWidth) + spacing // 8 ports + 1 gap between groups
	startX := (m.width - rowWidth) / 2

	if x < startX || x > startX+rowWidth {
		return -1
	}

	relativeX := x - startX

	// Determine which row (top: ports 0-7, bottom: ports 8-15)
	var rowOffset int
	if y <= 12 { // Approximate middle point
		rowOffset = 0 // Top row (ports 0-7)
	} else {
		rowOffset = 8 // Bottom row (ports 8-15)
	}

	// Determine which port within the row
	if relativeX < 4*portWidth {
		// First group (ports 0-3 or 8-11)
		portInGroup := relativeX / portWidth
		if portInGroup < 4 {
			return rowOffset + portInGroup
		}
	} else if relativeX >= 4*portWidth+spacing {
		// Second group (ports 4-7 or 12-15)
		relativeX -= 4*portWidth + spacing
		portInGroup := relativeX / portWidth
		if portInGroup < 4 {
			return rowOffset + 4 + portInGroup
		}
	}

	return -1
}

// processDiagnosticData processes the raw diagnostic output
func (m *Model) processDiagnosticData() {
	// Parse interface status information
	for line := range strings.SplitSeq(m.diagnosticData, "\n") {
		// Parse interface lines like "3: ici0: <BROADCAST,MULTICAST,UP,LOWER_UP>"
		if strings.Contains(line, "ici") && strings.Contains(line, ":") {
			portNum := m.extractPortNumber(line)
			if portNum >= 0 && portNum < 16 {
				if strings.Contains(line, "UP,LOWER_UP") {
					m.ports[portNum].Status = PortStatusUp
					m.ports[portNum].Info = "Link UP - Active"
				} else if strings.Contains(line, "DOWN") {
					m.ports[portNum].Status = PortStatusDown
					m.ports[portNum].Info = "Link DOWN - Inactive"
				} else {
					m.ports[portNum].Status = PortStatusError
					m.ports[portNum].Info = "Link ERROR - Check connection"
				}
			}
		}

		// Parse network statistics for error detection
		if strings.Contains(line, "ici") && strings.Contains(line, "errs") {
			fields := strings.Fields(line)
			if len(fields) >= 4 {
				portNum := m.extractPortNumberFromInterface(fields[0])
				if portNum >= 0 && portNum < 16 {
					// Check for errors in receive/transmit
					if fields[3] != "0" || (len(fields) >= 10 && fields[9] != "0") {
						if m.ports[portNum].Status == PortStatusUp {
							m.ports[portNum].Status = PortStatusError
							m.ports[portNum].Info = "Link UP - Errors detected"
						}
					}
				}
			}
		}
	}

	// Simulate some ports for demonstration if no real data
	if !strings.Contains(m.diagnosticData, "ici") {
		m.simulatePortStatuses()
	}
}

// extractPortNumber extracts port number from interface description
func (m Model) extractPortNumber(line string) int {
	// Look for patterns like "ici0:", "ici1:", etc.
	if idx := strings.Index(line, "ici"); idx != -1 {
		start := idx + 3
		end := start
		for end < len(line) && line[end] >= '0' && line[end] <= '9' {
			end++
		}
		if end > start {
			if portStr := line[start:end]; len(portStr) > 0 {
				var portNum int
				if n, _ := fmt.Sscanf(portStr, "%d", &portNum); n == 1 {
					return portNum
				}
			}
		}
	}
	return -1
}

// extractPortNumberFromInterface extracts port number from interface name in stats
func (m Model) extractPortNumberFromInterface(interfaceName string) int {
	interfaceName = strings.TrimSuffix(interfaceName, ":")
	if portStr, found := strings.CutPrefix(interfaceName, "ici"); found {
		var portNum int
		if n, _ := fmt.Sscanf(portStr, "%d", &portNum); n == 1 {
			return portNum
		}
	}
	return -1
}

// simulatePortStatuses creates realistic port statuses for demonstration
func (m *Model) simulatePortStatuses() {
	// Simulate a realistic network scenario
	statuses := []int{
		PortStatusUp, PortStatusUp, PortStatusDown, PortStatusUp,
		PortStatusUp, PortStatusError, PortStatusUp, PortStatusDown,
		PortStatusUp, PortStatusUp, PortStatusUp, PortStatusDown,
		PortStatusError, PortStatusUp, PortStatusDown, PortStatusUp,
	}

	infos := []string{
		"Active - 10Gbps", "Active - 10Gbps", "No Link", "Active - 10Gbps",
		"Active - 25Gbps", "CRC Errors", "Active - 10Gbps", "Cable Fault",
		"Active - 40Gbps", "Active - 10Gbps", "Active - 25Gbps", "No Signal",
		"Temp Warning", "Active - 10Gbps", "Disconnected", "Active - 40Gbps",
	}

	for i := range 16 {
		m.ports[i].Status = statuses[i]
		m.ports[i].Info = infos[i]
	}
}

// Commands
func tickCmd() tea.Cmd {
	return tea.Tick(time.Second*5, func(t time.Time) tea.Msg {
		return tickMsg(t)
	})
}

func runDiagnosticCmd() tea.Cmd {
	return func() tea.Msg {
		var output strings.Builder
		timestamp := time.Now().Format("15:04:05")
		output.WriteString(fmt.Sprintf("=== ICI Network Test Suite - %s ===\n\n", timestamp))

		// Generate 5 example tests with machine names and ICI ports
		tests := []struct {
			name        string
			machine     string
			iciPorts    string
			status      string
			description string
		}{
			{"Bandwidth Stress Test", "aabbc12", "ici0,ici1", "RUNNING", "Testing 40Gbps throughput on dual ports"},
			{"Latency Measurement", "xxyyz34", "ici2", "PASSED", "Sub-microsecond latency verification"},
			{"Error Rate Analysis", "mmnnp56", "ici0-ici3", "FAILED", "Packet loss detected on port ici2"},
			{"Link Stability Test", "qqrrs78", "ici1", "PASSED", "24-hour continuous operation test"},
			{"Optical Power Check", "ttuvw90", "ici0,ici2", "WARNING", "Port ici2 power below threshold"},
		}

		output.WriteString("--- Active Test Results ---\n")
		for i, test := range tests {
			statusColor := ""
			switch test.status {
			case "PASSED":
				statusColor = "✓"
			case "FAILED":
				statusColor = "✗"
			case "RUNNING":
				statusColor = "⟳"
			case "WARNING":
				statusColor = "⚠"
			}

			output.WriteString(fmt.Sprintf("%d. %s [%s]\n", i+1, test.name, test.machine))
			output.WriteString(fmt.Sprintf("   Ports: %s | Status: %s %s\n", test.iciPorts, statusColor, test.status))
			output.WriteString(fmt.Sprintf("   %s\n\n", test.description))
		}

		// Add some system info
		output.WriteString("--- System Information ---\n")
		output.WriteString("ICI Driver Version: 2.1.4\n")
		output.WriteString("Firmware: 1.8.2\n")
		output.WriteString("Active Connections: 12/16 ports\n")
		output.WriteString("Total Throughput: 320 Gbps\n")
		output.WriteString("Uptime: 15d 8h 42m\n\n")

		output.WriteString("--- Recent Events ---\n")
		output.WriteString("03:57:35 - Port ici2 on mmnnp56: CRC error detected\n")
		output.WriteString("03:57:28 - Test 'Bandwidth Stress Test' started on aabbc12\n")
		output.WriteString("03:57:15 - Port ici1 on qqrrs78: Link stability test completed\n")
		output.WriteString("03:57:02 - System health check: All critical systems operational\n")

		return diagnosticMsg(output.String())
	}
}

// updateSelectedICIs updates the selected ICI ports based on the selected QSFP ports
func (m *Model) updateSelectedICIs() {
	// Clear existing selections
	m.selectedICIs = make([]string, 0)

	// Convert selected QSFP ports to ICI ports
	for portIdx := range m.selectedPorts {
		iciPort := fmt.Sprintf("ici%d", portIdx+1)
		m.selectedICIs = append(m.selectedICIs, iciPort)
	}
}

// runTestCmd runs a test command with the specified machine and ICI port
func runTestCmd(machine, iciPort string) tea.Cmd {
	return func() tea.Msg {
		var output strings.Builder
		timestamp := time.Now().Format("15:04:05")

		if machine == "" {
			output.WriteString(fmt.Sprintf("=== Test Error - %s ===\n\n", timestamp))
			output.WriteString("ERROR: Machine name is required\n")
			output.WriteString("Please enter a machine name (e.g., aabbc12) and try again.\n")
		} else {
			output.WriteString(fmt.Sprintf("=== Running Test on %s - %s ===\n\n", machine, timestamp))
			output.WriteString(fmt.Sprintf("Target Machine: %s\n", machine))
			output.WriteString(fmt.Sprintf("Selected ICI Port: %s\n\n", iciPort))

			// Simulate test execution
			output.WriteString("--- Test Execution ---\n")
			output.WriteString(fmt.Sprintf("Connecting to %s...\n", machine))
			output.WriteString(fmt.Sprintf("Initializing %s interface...\n", iciPort))
			output.WriteString("Running bandwidth test...\n")
			output.WriteString("Measuring latency...\n")
			output.WriteString("Checking error rates...\n\n")

			output.WriteString("--- Test Results ---\n")
			output.WriteString("✓ Connection: ESTABLISHED\n")
			output.WriteString("✓ Bandwidth: 25.6 Gbps\n")
			output.WriteString("✓ Latency: 0.8 μs\n")
			output.WriteString("✓ Error Rate: 0.00%\n")
			output.WriteString("✓ Status: PASSED\n\n")

			output.WriteString(fmt.Sprintf("Test completed successfully on %s:%s\n", machine, iciPort))
		}

		return diagnosticMsg(output.String())
	}
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func main() {
	p := tea.NewProgram(initialModel(), tea.WithAltScreen(), tea.WithMouseAllMotion())
	if _, err := p.Run(); err != nil {
		log.Fatal(err)
	}
}
