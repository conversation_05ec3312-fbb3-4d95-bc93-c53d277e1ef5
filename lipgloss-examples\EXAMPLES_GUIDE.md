# Lipgloss Examples Guide

This directory contains the complete Lipgloss repository with examples that can help you improve your TUI application.

## Key Examples for Your Network Diagnostics TUI

### 1. Layout Example (`examples/layout/main.go`)
- **What it shows**: Comprehensive layout with tabs, dialogs, lists, status bars
- **Useful for**: Learning how to create complex layouts with proper alignment
- **Key techniques**:
  - `lipgloss.JoinHorizontal()` and `lipgloss.JoinVertical()` for combining elements
  - Status bar creation with proper width calculations
  - Dialog boxes and button styling
  - Tab navigation styling

### 2. Table Examples (`examples/table/`)
- **Available examples**:
  - `languages/main.go` - Multi-language table with alternating row styles
  - `pokemon/main.go` - Pokemon data table
  - `chess/main.go` - Chess board representation
- **Useful for**: Displaying structured data like your port information
- **Key techniques**:
  - Table borders and styling
  - Alternating row colors
  - Header styling
  - Cell alignment

### 3. List Examples (`examples/list/`)
- **Available examples**:
  - `simple/main.go` - Basic list creation
  - `grocery/main.go` - Grocery list with checkboxes
  - `sublist/main.go` - Nested lists
- **Useful for**: Your ICI ports list and test results
- **Key techniques**:
  - List enumerators (bullets, numbers, roman numerals)
  - Nested lists
  - Custom list styling

## Running Examples

To run any example:

```bash
cd lipgloss-examples/examples/[example-name]
go run main.go
```

For example:
```bash
cd lipgloss-examples/examples/layout
go run main.go
```

## Key Lipgloss Concepts Demonstrated

### Border and Alignment Issues (Your Current Problem)
The layout example shows proper techniques for:
- Width calculations that account for borders and padding
- Using `lipgloss.Width()` to measure rendered content
- Proper alignment within containers

### Status Bars
The layout example includes a sophisticated status bar that:
- Calculates remaining width dynamically
- Handles multiple sections with proper spacing
- Uses consistent styling

### Color and Styling
Examples show:
- Adaptive colors for light/dark themes
- Color gradients and blending
- Consistent style inheritance

## Specific Techniques for Your TUI

### 1. Fix Border Alignment
Look at how the layout example handles width calculations:
```go
w := lipgloss.Width
statusVal := statusText.
    Width(width - w(statusKey) - w(encoding) - w(fishCake)).
    Render("Ravishing")
```

### 2. Better Panel Layout
The layout example shows how to create side-by-side panels:
```go
lists := lipgloss.JoinHorizontal(lipgloss.Top, leftPanel, rightPanel)
```

### 3. Consistent Styling
Define styles once and reuse them:
```go
var (
    baseStyle = lipgloss.NewStyle().Foreground(normal)
    borderStyle = lipgloss.NewStyle().BorderForeground(highlight)
)
```

## Next Steps

1. **Study the layout example** - It's the most comprehensive
2. **Run the table examples** - See if tables would work better for your port display
3. **Experiment with the list examples** - For your ICI ports and test results
4. **Apply the width calculation techniques** - To fix your border alignment issues

The examples are fully functional and can be run immediately to see the visual results.
