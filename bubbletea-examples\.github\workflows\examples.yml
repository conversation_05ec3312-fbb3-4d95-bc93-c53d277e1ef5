name: examples

on:
  push:
    branches:
      - 'master'
    paths:
      - '.github/workflows/examples.yml'
      - './examples/go.mod'
      - './examples/go.sum'
      - './tutorials/go.mod'
      - './tutorials/go.sum'
      - './go.mod'
      - './go.sum'
  workflow_dispatch: {}

jobs:
  tidy:
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: '^1'
          cache: true
      - shell: bash
        run: |
          (cd ./examples && go mod tidy)
          (cd ./tutorials && go mod tidy)
      - uses: stefanzweifel/git-auto-commit-action@v6
        with:
          commit_message: "chore: go mod tidy tutorials and examples"
          branch: master
          commit_user_name: actions-user
          commit_user_email: <EMAIL>

