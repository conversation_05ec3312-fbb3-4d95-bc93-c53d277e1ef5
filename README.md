# ICI Network Diagnostics TUI

A Terminal User Interface (TUI) application built with Go and Bubbletea for diagnosing ICI (Inter-Chip Interconnect) network links using fiber optic connections.

## Features

- **Visual Switch Representation**: 16 QSFP ports arranged horizontally in a single row
- **Real-time Status Monitoring**: Live updates of port status with color-coded indicators
- **Mouse Support**: Click on ports to select them for detailed information
- **Keyboard Navigation**: Use arrow keys to navigate between ports
- **Diagnostic Output**: Real-time CLI command execution and output processing
- **Status Legend**: Clear visual indicators for port states (UP, DOWN, ERROR, UNKNOWN)

## Port Status Indicators

Each port is exactly 4 characters wide and 1 character high:

- **●●●● (Green)**: Port UP - Active connection
- **○○○○ (Red)**: Port DOWN - No connection
- **▲▲▲▲ (Orange)**: Port ERROR - Connection issues detected
- **---- (Gray)**: Port UNKNOWN - Status not determined

## Installation

1. Ensure you have Go installed (version 1.19 or later)
2. Clone or download this repository
3. Install dependencies:
   ```bash
   go mod tidy
   ```

## Usage

Run the application:
```bash
go run main.go
```

### Controls

- **Left/Right Arrow Keys**: Navigate between ports horizontally
- **Home**: Jump to first port (Port 1)
- **End**: Jump to last port (Port 16)
- **Mouse**: Click on ports to select them
- **Q or Ctrl+C**: Quit the application

## Customization

### Adding Your Own Diagnostic Commands

The application currently simulates diagnostic data. To integrate with your actual ICI network tools, modify the `runDiagnosticCmd()` function in `main.go`:

```go
func runDiagnosticCmd() tea.Cmd {
    return func() tea.Msg {
        // Replace these with your actual diagnostic commands
        commands := []struct {
            name string
            cmd  []string
        }{
            {"Your Command", []string{"your-tool", "args"}},
            // Add more commands as needed
        }
        // ... rest of the function
    }
}
```

### Customizing Port Parsing

Update the `processDiagnosticData()` function to parse your specific CLI output format:

```go
func (m *Model) processDiagnosticData() {
    lines := strings.Split(m.diagnosticData, "\n")
    
    // Add your parsing logic here
    for _, line := range lines {
        // Parse your specific output format
        // Update port statuses accordingly
    }
}
```

## Architecture

The application is structured around the Bubbletea framework:

- **Model**: Holds the application state (ports, selected port, diagnostic data)
- **Update**: Handles user input and state changes
- **View**: Renders the UI components

### Key Components

1. **Switch Visualization**: Renders the 4x4 port layout with status indicators
2. **Port Selection**: Handles keyboard and mouse input for port navigation
3. **Diagnostic Engine**: Executes CLI commands and processes output
4. **Real-time Updates**: Periodic refresh of diagnostic data (every 5 seconds)

## Dependencies

- [Bubbletea](https://github.com/charmbracelet/bubbletea): TUI framework
- [Lipgloss](https://github.com/charmbracelet/lipgloss): Styling and layout

## Development

To modify the application:

1. **Port Layout**: Modify `renderSwitch()` function
2. **Styling**: Update the Lipgloss styles throughout the code
3. **Diagnostic Logic**: Customize `runDiagnosticCmd()` and `processDiagnosticData()`
4. **Update Frequency**: Change the tick interval in `tickCmd()`

## Example Output

```
🔗 ICI Network Diagnostics 🔗

╔══════════════════════════════════════════════════════════════════════════════════╗
║                          ICI Network Switch - 16x QSFP Ports                     ║
║                                                                                  ║
║  ┌─1─┐ ┌─2─┐ ┌─3─┐ ┌─4─┐ ┌─5─┐ ┌─6─┐ ┌─7─┐ ┌─8─┐ ┌─9─┐ ┌10┐ ┌11┐ ┌12┐ ┌13┐ ┌14┐ ┌15┐ ┌16┐ ║
║  │●●●●│ │●●●●│ │○○○○│ │●●●●│ │●●●●│ │▲▲▲▲│ │●●●●│ │○○○○│ │●●●●│ │●●●●│ │●●●●│ │○○○○│ │▲▲▲▲│ │●●●●│ │○○○○│ │●●●●│ ║
║  └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ └────┘ ║
║                                                                                  ║
║  Legend: ●●●● UP | ○○○○ DOWN | ▲▲▲▲ ERROR | ---- UNKNOWN                        ║
╚══════════════════════════════════════════════════════════════════════════════════╝

╭─────────────────────────────────────────────────────────────────────────────────╮
│ Selected Port: 1 | Status: UP | Active - 10Gbps                                │
╰─────────────────────────────────────────────────────────────────────────────────╯

╭─────────────────────────────────────────────────────────────────────────────────╮
│ === ICI Network Diagnostics - 14:30:25 ===                                     │
│                                                                                 │
│ --- Interface Status ---                                                       │
│ 1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN           │
│ 2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP │
│ 3: ici0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 9000 qdisc mq state UP         │
│ 4: ici1: <BROADCAST,MULTICAST,DOWN> mtu 9000 qdisc mq state DOWN              │
╰─────────────────────────────────────────────────────────────────────────────────╯

Use arrow keys or mouse to select ports • q to quit | ● Updates: 5 | Last: 14:30:25
```

## License

This project is open source. Feel free to modify and distribute as needed.
